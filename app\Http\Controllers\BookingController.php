<?php

namespace App\Http\Controllers;

use App\Listing;
use App\Booking;
use App\Http\Requests\ConfirmBookingRequest;
use App\Mail\BookingCreateMail;
use App\Models\CurrencyConversionRate;
use App\Services\{BookingService, SkyflowService, StripeService, BillingAddressService, CancellationPolicyService, WalletService};
use App\Traits\UserNotificationTrait;
use Illuminate\Http\Request;
use App\Services\PayPalService;
use App\Traits\BookingTrait;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    use UserNotificationTrait, BookingTrait;
    function __construct(
        public BookingService $bookingService,
        public StripeService $stripeService,
        public BillingAddressService $billingAddressService,
        public SkyflowService $skyflowService,
        public WalletService $walletService
    ) {}
    function reserve_data(Request $request)
    {
        $listing = Listing::where("ids", $request->listing_id)->firstOrFail();
        $rules = [
            "check_in" => ["required", "date_format:Y-m-d"],
            "listing_id" => "required",
        ];

        $messages = [
            "check_in.required" => "Please choose the dates you would like to reserve",
            "check_in.date_format" => "The check-in date must be in YYYY-MM-DD format.",
        ];
        if ($listing) {
            $basis_type = $listing->detail->basis_type;
            if (!in_array($basis_type ?? "", ["Daily", "Hourly", "Tour"])) {
                return api_response(false, "Invalid basis type");
            }
            if ($basis_type === 'Daily') {
                $rules["check_out"] = ["required", "date_format:Y-m-d"];
                $messages["check_out.required"] = "Check-out date is required for daily basis listings.";
                $messages["check_out.date_format"] = "The check-out date must be in YYYY-MM-DD format.";
                if(in_array($listing->category_id, [3])){
                    $rules["check_in_time"] = ["required", ];
                    $rules["check_out_time"] = ["required", "after:check_in_time"];
                    $messages["check_in_time.required"] = "Please select a pick-up time to proceed.";
                    $messages["check_out_time.required"] = "Please select a drop-off time to proceed.";
                    $messages["check_out_time.after"] = "Your drop-off time must be later than the pick-up time.";
                }
            } elseif ($basis_type === "Hourly") {
                $rules["slots"] = ["required", "array"];
            } elseif ($basis_type === "Tour"){
                $rules["adult_number"] = ["required", "numeric"];
                $rules["child_number"] = ["numeric"];
                $rules["tour_type"] = ["required", "in:guests,private"];
            }
            $validate = Validator::make($request->all(), $rules, $messages);
            if ($validate->fails()) {
                return ["status" => false, "message" => $validate->errors()->first()];
            } else {
                if (auth()->user()->identity_verified == "verified") {
                    if ($listing) {
                        // Cut off time 
                        $bookingCloseTime = $listing->detail->booking_close_time;
                        $checkInDate = $request->check_in;
                        $timezone = new DateTimeZone('America/Bogota');
                        $currentTime = new DateTime('now', $timezone);
                        $currentDate = $currentTime->format('Y-m-d');
                        if (isset($bookingCloseTime)) {
                            $checkInDateTime = DateTime::createFromFormat('Y-m-d', $checkInDate, $timezone);
                            if ($checkInDateTime && $checkInDateTime->format('Y-m-d') === $currentDate) {
                                $closeTime = DateTime::createFromFormat('h:i A', $bookingCloseTime, $timezone);
                                if (!$closeTime) {
                                    return api_response(false, "Invalid booking close time format");
                                }
                                $closeTime->setDate($currentTime->format('Y'), $currentTime->format('m'), $currentTime->format('d'));
                                if ($currentTime > $closeTime) {
                                    // $message = "Booking is closed for today. Current time: " . $currentTime->format('H:i:s') . " and Close time: " . $closeTime->format('H:i:s');
                                    $message = "Booking is Closed For Today.";
                                    return api_response(false, $message);
                                }
                            }
                        }
                        // Cut off time end 
                        if ($basis_type == "Daily") {
                            $start_date = $request->check_in ?? $request->check_out;
                            $end_date = $request->check_out ?? $request->check_in;
                            $checkIn = Carbon::parse($start_date);
                            $checkOut = Carbon::parse($end_date);
                            $check_in_time = $request->check_in_time ? date("g:i A", strtotime($request->check_in_time)) : null;
                            $check_out_time = $request->check_out_time ? date("g:i A", strtotime($request->check_out_time)) : null;
                            if($listing->category_id == 4){
                                $totalDays = $checkIn->diffInDays($checkOut);
                            }else{
                                $totalDays = $checkIn->diffInDays($checkOut) + 1;
                            }
                            // ======================== Minimun stay ======================== //
                            if (($listing->detail->minimum_stay_length ?? 0) > $totalDays) {
                                return api_response(false, "Minimum stay length is " . $listing->detail->minimum_stay_length . " days");
                            }
                            // ======================== Minimun stay end ==================== //

                            // Daily Data
                            $data = [
                                "check_in" => $request->check_in,
                                "check_out" => $request->check_out ?? $request->check_in,
                                "check_in_time" => $check_in_time,
                                "check_out_time" => $check_out_time,
                                'guests' => $request->guest ?? $request->guests,
                                'total_days' => $totalDays ?? 1,
                                'total_hours' => $totalHours ?? 1,
                                'listing_ids' => $listing->ids,
                            ];
                        } elseif ($basis_type == "Hourly") {
                            $hourlyAvailabilities = $listing->hourly_availabilities;
                            $slots = $request->slots;
                            $totalHours = count($slots);
                            $availableSlots = collect($hourlyAvailabilities)->pluck('full_time')->toArray();
                            $missingSlots = array_diff($slots, $availableSlots);
                            if (!empty($missingSlots)) {
                                return api_response(false, "Some selected slots are not available");
                            }
                            // Hourly Data
                            $data = [
                                "check_in" => $request->check_in,
                                "check_out" => $request->check_out ?? $request->check_in,
                                "hour_slots" => $slots,
                                "total_hours" => $totalHours,
                                "total_days" => 1,
                                "listing_ids" => $listing->ids,
                            ];
                        } elseif($basis_type == "Tour"){
                            $adult_number = $request->adult_number ?? 1;
                            $child_number = $request->child_number ?? 0;
                            $total_guests = $adult_number + $child_number;
                            $tour_type = $request->tour_type;
                            $booking_capacity = $listing->detail->booking_capacity ?? null;
                            if($booking_capacity){
                                $active_booking = Booking::where("listing_basis", "Tour")->where("check_in", $request->check_in)->where("listing_id", $listing->id)->where("status", 0)->sum("guest");
                                $remaining_capacity = $booking_capacity - $active_booking;
                                if ($total_guests > $remaining_capacity) {
                                    return api_response(false, "Not enough capacity available. Only $remaining_capacity spots left.");
                                }
                            }
                            $data = [
                                "check_in" => $request->check_in,
                                "check_out" => $request->check_in,
                                "adult_number" => $adult_number,
                                "child_number" => $child_number,
                                "total_guests" => $total_guests,
                                "tour_type" => $tour_type,
                                "total_days" => 1,
                                "total_hours" => 1,
                                "listing_ids" => $listing->ids,
                            ];
                        }
                        session()->put("reserve", $data);
                        return ["status" => true, "message" => "Reserve added successfully", "url" => route("confirm_booking", ["locale" => app()->getLocale()])];
                    } else {
                        return ["status" => false, "message" => "No listing found against this id"];
                    }
                } else {
                    return ["status" => false, "message" => translate('user_account_setting.verify_identity_msg'),  "url" => route("webaccount_setting", ["locale" => app()->getLocale()]) . "#verfication-kyc"];
                }
            }
        }
    }
    function currencyConvert($amount)
    {
        //$usd_conversion_rate = config('currency.conversion_rate');
        $usd_conversion_rate = CurrencyConversionRate::where('target_currency', 'USD')->value('rate');
        if (empty($usd_conversion_rate) || $usd_conversion_rate <= 0) {
            throw new \Exception('Invalid or unavailable conversion rate. Please update the conversion rate.');
        }
        $convertedAmount = $amount * $usd_conversion_rate;
        return round($convertedAmount, 2);
    }
    public function confirm_booking(Request $request)
    {
        $payment_intent = $request->query('payment_intent');
        if ($payment_intent) {
            $payment_intent_retrieve = $this->stripeService->payment_intend_retrieve($payment_intent);
            if ($payment_intent_retrieve->status != 'succeeded') {
                return redirect()->route('confirm_booking', ["locale" => app()->getLocale()])->with(["type" => "warning", "title" => "Not Authorize", "message" => "Your Payment has not Authorize to Make Booking"]);
            }
            $reserve = session()->get("reserve");
            if (!$reserve) {
                return redirect()->route('confirm_booking', ["locale" => app()->getLocale()])->with(["type" => "warning", "title" => "Expire", "message" => "Url has been Expired"]);
            }
            $listing = Listing::where('ids', $reserve["listing_ids"])->firstOrFail();
            if (isset($payment_intent)) {
                $retrieve_charge = $this->stripeService->retrive_charge($payment_intent_retrieve->latest_charge);
                if ($retrieve_charge["status"] == false) {
                    return back()->with(["type" => "error", "message" => $retrieve_charge["message"]]);
                }
                $request_data = session("request_data");
                $data = array_merge($request_data, $reserve);
                $bookingService = $this->bookingService->create_booking($data, $listing, $payment_intent, $payment_intent_retrieve->latest_charge, $retrieve_charge);
                if ($bookingService["status"] == true) {
                    $booking = $bookingService["data"];
                    // send notification to service provider
                    $this->sendBookingNotification($listing->user_id, $booking->id);
                    // save billing address
                    // $this->billingAddressService->create($booking, $request->all());
                    session()->forget("reserve");
                    session()->forget("request_data");
                    return redirect()->route("bookings", ["locale" => app()->getLocale()])->with(["type" => "success", "title" => translate("confirm_booking.confirm_booking_title"), "message" => translate("confirm_booking.confirm_booking_message")]);
                } else {
                    return redirect()->route('confirm_booking', ["locale" => app()->getLocale()])->with(["type" => "error", "title" => "Error", "message" => "Something went wrong in booking"]);
                }
            } else {
                return redirect()->route('confirm_booking', ["locale" => app()->getLocale()])->with(["type" => "warning", "title" => "Expire", "message" => "Intent not found"]);
            }
        }
        $reserve = session("reserve");
        if (!$reserve) {
            return redirect("/")->with([
                "type" => "error",
                "message" => "Start a new reservation to proceed.",
                "title" => "No Active Booking Found!"
            ]);
        }
        $cards = $this->skyflowService->get_all_cards();
        $listing = Listing::where("ids", $reserve["listing_ids"])->firstOrFail();
        $usd_conversion_rate = CurrencyConversionRate::where('target_currency', 'USD')->value('rate');
        if ($usd_conversion_rate !== null) {
        } else {
            $usd_conversion_rate = 1;
        }
        //$usd_conversion_rate = config('currency.conversion_rate');
        $currency = session('currency', 'COP');
        $conversion_rate = session("conversion_rate", 1);
        $listing_price = season_price($reserve["check_in"], $listing->base_price, $listing->seasons);
        $basis_type = $listing->detail->basis_type;
        // Total amount calculation
        if ($basis_type == "Hourly") {
            $total_amount = $listing_price * $reserve["total_hours"];
        } elseif($basis_type == "Daily") {
            $total_amount = $listing_price * $reserve["total_days"];
        } elseif($basis_type == "Tour") {
            $adult_number = $reserve["adult_number"] ?? 1;
            $child_number = $reserve["child_number"] ?? 0;
            $start_date = $reserve["check_in"];
            if($reserve["tour_type"] == "guests"){
                $reserve["adult_price_base"] = season_price($start_date, $listing->detail->adult_price, $listing->seasons);
                $reserve["child_price_base"] = season_price($start_date, $listing->detail->child_price, $listing->seasons);
                $reserve["adult_price"] = round($reserve["adult_price_base"] * $conversion_rate);
                $reserve["child_price"] = round(($reserve["child_price_base"] ?? 0) * $conversion_rate);
                $total_amount = ($reserve["adult_price_base"] * $adult_number) + ($reserve["child_price_base"] * $child_number);
            }else{
                $reserve["private_price_base"] = season_price($start_date, $listing->detail->private_booking_price, $listing->seasons);
                $total_amount = $reserve["private_price_base"];
            }
        }
        $sub_total = $total_amount;

        // weekly / monthly discount
        $weekly_monthly_discount = weeklyMonthlyDiscount($reserve["total_days"], $total_amount, $listing->discount);
        if (isset($weekly_monthly_discount["discount_amount"])) {
            $sub_total -= $weekly_monthly_discount["discount_amount"];
        }
        // weekly / monthly discount end

        // new listing discount
        $new_listing_discount = newListingDiscount($total_amount, $listing->bookings()->count(), $listing->discount);
        if (isset($new_listing_discount["discount_amount"])) {
            $sub_total -= $new_listing_discount["discount_amount"];
        }
        // new listing discount end

        $total_usd_amount = $sub_total * $usd_conversion_rate;
        $data = compact("listing", "reserve", "cards", "total_usd_amount", 'sub_total', 'new_listing_discount', 'weekly_monthly_discount', "total_amount", 'currency', "conversion_rate", "listing_price", "basis_type");
        return view('website.confirm-booking', $data);
    }
    public function confirm_booking_add(ConfirmBookingRequest $request, $listingIds)
    {
        try {
            $listing = Listing::where("ids", $listingIds)->firstOrFail();
            $reserve = session("reserve");
            //$usdConversionRate = config("currency.conversion_rate");
            $usdConversionRate = CurrencyConversionRate::where('target_currency', 'USD')->value('rate');
            if ($usdConversionRate !== null) {
            } else {
                $usdConversionRate = 1;
            }
            if (!$listing || !$reserve) {
                return back()->with(["type" => "error", "message" => "Listing or reservation data not found"]);
            }
            $basis_type = $listing->detail->basis_type;
            $listing_price = season_price($reserve["check_in"], $listing->base_price, $listing->seasons);
            $reserve['currency']  = session('currency', 'COP');
            $reserve['conversion_rate'] = session("conversion_rate", 1);
            // Total amount calculation
            if ($basis_type == "Hourly") {
                $totalAmount = $listing_price * $reserve["total_hours"];
            } elseif($basis_type == "Daily") {
                $totalAmount = $listing_price * $reserve["total_days"];
            } elseif ($basis_type == "Tour") {
                $adult_number = $reserve["adult_number"] ?? 1;
                $child_number = $reserve["child_number"] ?? 0;
                $start_date = $reserve["check_in"];
                if($reserve["tour_type"] == "guests"){
                    $reserve["adult_price_base"] = season_price($start_date, $listing->detail->adult_price, $listing->seasons);
                    $reserve["child_price_base"] = season_price($start_date, $listing->detail->child_price, $listing->seasons);
                    $reserve["adult_price"] = round($reserve["adult_price_base"] * $reserve['conversion_rate']);
                    $reserve["child_price"] = round(($reserve["child_price_base"] ?? 0) * $reserve['conversion_rate']);
                    $totalAmount = ($reserve["adult_price_base"] * $adult_number) + ($reserve["child_price_base"] * $child_number);
                }else{
                    $reserve["private_price_base"] = season_price($start_date, $listing->detail->private_booking_price, $listing->seasons);
                    $totalAmount = $reserve["private_price_base"];
                }
            }
            $sub_total = $totalAmount;
            // Total amount calculation end

            // weekly / monthly discount
            $weekly_monthly_discount = weeklyMonthlyDiscount($reserve["total_days"], $totalAmount, $listing->discount);
            if (isset($weekly_monthly_discount["discount_amount"])) {
                $sub_total -= $weekly_monthly_discount["discount_amount"];
            }
            // weekly / monthly discount end
            // new listing discount
            $new_listing_discount = newListingDiscount($totalAmount, $listing->bookings()->count(), $listing->discount);
            if (isset($new_listing_discount["discount_amount"])) {
                $sub_total -= $new_listing_discount["discount_amount"];
            }
            // new listing discount end

            // new listing discount 
            $totalUsdAmount = $sub_total * $usdConversionRate;
            $reserve['usd_conversion_amount'] = $totalUsdAmount;
            $reserve['total_amount'] = $totalAmount;
            $reserve['sub_total'] = $sub_total;
            session()->put("reserve", $reserve);
            session()->put("request_data", $request->all());
            if ($totalUsdAmount < 0.50) {
                return back()->with(["type" => "warning", "message" => "Amount must be equal to or greater than 0.50 USD"]);
            }
            if ($request->input('payment-method') === 'paypal') {
                return $this->processPayPalPayment($request, $listing, $totalUsdAmount);
            }
            return $this->processCardPayment($request, $listing, $reserve, $totalUsdAmount);
        } catch (\Exception $e) {
            return back()->with(["type" => "error", "message" => $e->getMessage()]);
        }
    }

    private function processPayPalPayment($request, $listing, $totalUsdAmount)
    {
        $paypalService = new PayPalService();
        $totalAmount = $totalUsdAmount;
        session()->put("request_data", $request->all());

        $redirectUrl = $paypalService->createPayment($totalAmount, $listing->name);
        if ($redirectUrl) {
            return redirect()->away($redirectUrl);
        }
        return back()->with(["type" => "error", "message" => "Unable to process PayPal payment"]);
    }

    private function processCardPayment($request, $listing, $reserve, $totalUsdAmount)
    {
        $cardDetails = $this->skyflowService->card_detail($request->card_id);
        if (!$cardDetails->successful() || !isset($cardDetails["fields"])) {
            return back()->with(["type" => "error", "message" => "Card not found"]);
        }
        $paymentMethod = $this->skyflowService->payment_method($cardDetails["fields"], $request->cvc);
        if (!isset($paymentMethod["id"])) {
            return back()->with(["type" => "error", "message" => "Invalid card details"]);
        }
        $paymentIntent = $this->stripeService->payment_intend($totalUsdAmount, $paymentMethod["id"]);


        // $receiptUrl = $retrieve_charge["data"]["receipt_url"] ?? null;
        if ($paymentIntent->status === 'requires_action' && isset($paymentIntent->next_action->redirect_to_url->url)) {
            return redirect()->away($paymentIntent->next_action->redirect_to_url->url);
        }
        $retrieve_charge = $this->stripeService->retrive_charge($paymentIntent->latest_charge);
        //$retrieve_charge['data']['payment_method_details']['card']['last4'];
        if ($retrieve_charge["status"] == false) {
            return back()->with(["type" => "error", "message" => $retrieve_charge["message"]]);
        }
        
        if ($paymentIntent && isset($paymentIntent["id"])) {
            $data = array_merge($reserve, $request->all());
            $bookingService = $this->bookingService->create_booking($data, $listing, $paymentIntent->id, $paymentIntent->latest_charge, $retrieve_charge);
            if ($bookingService["status"] === true) {
                $booking = $bookingService["data"];
                $this->sendBookingNotification($listing->user_id, $booking->id);
                session()->forget("reserve");
                return redirect()->route("bookings", ["locale" => app()->getLocale()])->with(["type" => "success", "title" => translate("confirm_booking.confirm_booking_title"), "message" => translate("confirm_booking.confirm_booking_message")]);
            } else {
                return back()->with(["type" => "error", "message" => "Booking failed"]);
            }
        }else{
            return back()->with(["type" => "error", "message" => "Payment Failed"]);
        }
    }

    public function paypalSuccess(Request $request)
    {
        $orderId = $request->query('token');
        $baseUrl = config('paypal.sandbox') ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';
        $paypalService = new PayPalService();
        $response = $paypalService->capturePayment($orderId, $baseUrl);
        if (isset($response['id']) && $response['status'] === 'COMPLETED') {
            $paymentDetails = $response;
            $captureId = null;
            if (!empty($response['purchase_units'])) {
                foreach ($response['purchase_units'] as $unit) {
                    if (isset($unit['payments']['captures'][0]['id'])) {
                        $captureId = $unit['payments']['captures'][0]['id'];
                        break;
                    }
                }
            }
            $reserve = session("reserve");
            $listing = Listing::where("ids", $reserve["listing_ids"])->firstOrFail();
            $data = array_merge(session("request_data"), $reserve);
            $bookingService = $this->bookingService->create_booking($data, $listing, $paymentDetails['id'], $captureId);
            if ($bookingService["status"] == true) {
                $booking = $bookingService["data"];
                $this->sendBookingNotification($listing->user_id, $booking->id);
                session()->forget("reserve");
                session()->forget("request_data");
                return redirect()->route("bookings", ["locale" => app()->getLocale()])->with(["type" => "success", "title" => translate("confirm_booking.confirm_booking_title"), "message" => translate("confirm_booking.confirm_booking_message")]);
            } else {
                dd("Something went worng in booking service");
            }
        }
        dd("processPayPalPayment erro");

        return redirect('/')->with(["type" => "error", "message" => "Unable to complete PayPal payment."]);
    }

    public function paypalCancel()
    {
        return redirect('/')->with(["type" => "warning", "message" => "Payment was cancelled."]);
    }

    public function bookingDetail($locale=null,$ids, $booking_number)
    {
        try {
            $booking = Booking::with([
                "listing" => function($query) {
                    $query->withTrashed();
                },
                "listing.detail",
                "listing.category",
                "listing.user" => function($query) {
                    $query->withTrashed();
                }
            ])
            ->where("ids", $ids)
            ->where("booking_number", $booking_number)
            ->where("user_id", auth()->id())
            ->firstOrFail();
            
            // Add prefix to deleted listing names
            // if ($booking->listing && $booking->listing->deleted_at) {
            //     $booking->listing->name = '[' . translate('user_bookings.deleted') . '] ' . $booking->listing->name;
            // }
            
            $category = $booking->listing->category;
            $listing = $booking->listing;
            $cancellationService = (new CancellationPolicyService)->calculateCancellationAmount($booking);
            return view('website.myBookingDetail', compact('category', 'listing', "booking", "cancellationService"));
        } catch (\Exception $e) {
             return redirect()->back()->with(["type" => "error","title"=> "Error", "message" => translate('confirm_booking.booking_not_found_error_msg')]);
             //return redirect()->route('bookings', ["locale" => app()->getLocale()])->with(["type" => "error","title"=> "Error", "message" => translate('confirm_booking.booking_not_found_error_msg')]);
        }
    }
    public function bookingPDF($ids, $booking_number)
    {
        try {
            $booking = Booking::with([
                "listing" => function($query) {
                    $query->withTrashed();
                },
                "listing.category",
                "listing.user" => function($query) {
                    $query->withTrashed();
                }
            ])
            ->where("ids", $ids)
            ->where("booking_number", $booking_number)
            ->where("user_id", auth()->id())
            ->firstOrFail();
            
            // Add prefix to deleted listing names
            if ($booking->listing && $booking->listing->deleted_at) {
                $booking->listing->name = '[' . translate('user_bookings.deleted') . '] ' . $booking->listing->name;
            }
            
            $category = $booking->listing->category;
            $listing = $booking->listing;
            $cancellationService = (new CancellationPolicyService)->calculateCancellationAmount($booking);
            return view('website.bookingPDF', compact('category', 'listing', "booking", "cancellationService"));
        } catch (\Exception $e) {
            return redirect()->back()->with(["type" => "warning", "message" => "Booking not Found"]);
        }
    }
    function tour_update_booking(Request $request, $listing_ids){
        $request->validate([
            "adult_number" => "required|numeric",
            "child_number" => "numeric",
        ]);
        $listing = Listing::where("ids", $listing_ids)->first();
        $reserve = session("reserve");
        $booking_capacity = $listing->detail->booking_capacity ?? null;
        $reserve["adult_number"] = $request->adult_number;
        $reserve["child_number"] = $request->child_number;
        $reserve["total_guests"] = $request->adult_number + $request->child_number;
        if($booking_capacity){
            $active_booking = Booking::where("listing_basis", "Tour")->where("check_in", $request->check_in)->where("listing_id", $listing->id)->where("status", 0)->sum("guest");
            $remaining_capacity = $booking_capacity - $active_booking;
            if ($reserve["total_guests"] > $remaining_capacity) {
                return api_response(false, "Not enough capacity available. Only $remaining_capacity spots left.");
            }
        }
        session()->put("reserve",$reserve);
        return api_response(true, "Updated reserve");
    }
    function cancelBooking($booking_id)
    {

        $bookingService = $this->bookingService->cancel_booking(booking_id: $booking_id);
        if (($bookingService["status"] ?? null) == false) {
            return back()->with(["type" => "error", "message" => $bookingService["message"], "title" => "Oops!"]);
        }
        return back()->with(["type" => "success", "message" => $bookingService["message"], "title" => "Success!"]);
    }
    function generate_booking_pdf($booking_id){
        $booking = Booking::with("customer", "listing", "provider", "discounts","detail")->where("ids",$booking_id)->firstOrFail();
        $listing = $booking->listing;
        $provider = $booking->provider;
        $pdf = Pdf::loadView('pdf.booking', compact('booking', "listing", 'provider'))->setPaper('a4', 'landscape');
        $pdf_name = $booking->booking_number . ".pdf";
        return $pdf->download($pdf_name);
    }
    function cancelBookingAdmin(Request $request)
    {

        $request->validate([
            "booking_id" => "required|exists:bookings,ids",
            "refundValue" => "required|numeric",
            "deductProvider" => "required|in:yes,no"
        ]);
        $admin_cancellation = $this->bookingService->adminCancelBooking($request->all());
        //return $admin_cancellation;
        if ($admin_cancellation["status"]) {
            return back()->with(["type" => "success", "message" => "Your booking has been cancelled"]);
        } else {
            return back()->with(["type" => "error", "message" => $admin_cancellation["message"]]);
        }
    }
}
