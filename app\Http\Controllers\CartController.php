<?php

namespace App\Http\Controllers;

use App\Booking;
use App\Card;
use App\Listing;
use App\ListingDetail;
use App\Models\Cart;
use App\Models\User;
use App\Notifications\BookingNotification;
use App\Services\WalletService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Stripe\Charge;
use Stripe\Stripe;
use Stripe\Token;

use function PHPUnit\Framework\isNull;

class CartController extends Controller
{
    function cart_load()
    {
        $carts = Cart::with("listing", "user")->where("user_id", auth()->id())->get();
        return view('website.cart', compact("carts"));
    }
    function get_cart()
    {
        $carts = Cart::with("listing")->where("user_id", auth()->id())->get();
        if ($carts->isEmpty()) {
            return api_response(true, "No cart found");
        } else {
            return api_response(true, "Cart found", $carts);
        }
    }
    function add_cart(Request $request)
    {
        try{
            $validate = Validator::make($request->all(), [
                "listing_id" => "required",
                "check_in" => "required",
            ], [
                "check_in.required" => "Please choose the dates you would like to reserve"
            ]);
            if ($validate->fails()) {
                return ["status" => false, "message" => $validate->errors()->first(), "data" => null];
            }
            $listing = Listing::find($request->listing_id);
            if ($listing) {
                $request_data = $request->all();
                $total_amount = $request_data["total_amount"];
                $request_data["subtotal"] = $total_amount;
                $request_data["user_id"] = auth()->id();
                // Tour category 
                if ($listing->category_id == 1) {
                    $validate = Validator::make($request->all(), [
                        "adult_number" => "required|numeric",
                        "child_number" => "numeric",
                    ]);
                    if ($validate->fails()) {
                        return ["status" => false, "message" => $validate->errors()->first(), "data" => null];
                    }
                    $request_data["basis_type"] = "Tour";
                    // ========== group discount ========== //
                    $total_member = $request_data["adult_number"] + $request_data["child_number"];
                    if($total_member >= $listing->detail->no_group_people && $listing->detail->group_discount){
                        $group_discount = get_percentage($listing->detail->group_discount, $total_amount);
                        $total_amount = round($total_amount - $group_discount);
                        $request_data["group_discount_amount"] = $group_discount;
                    }
                    // ========== group discount end ========== //
                } elseif ($listing->detail->basis_type == "Hourly") {
                    $request_data["basis_type"] = "Hourly";
                } elseif ($listing->detail->basis_type = "Daily") {
                    $request_data["basis_type"] = "Daily";
                    $minimum_stay_length = $listing->detail->minimum_stay_length??null;
                    if(isset($minimum_stay_length) && $request->total_days < $minimum_stay_length ){
                        return ["status" => false, "message" => "Minimun Stay Lenght is $minimum_stay_length days", "data" => null];
                    }

                    // ========== monthly and weekly discount  ========== //
                    $total_days = $request_data['total_days']; 
                    $monthly_discount = $listing->detail->monthly_discount ?? null;
                    $weekly_discount = $listing->detail->weekly_discount ?? null;

                    $request_data["duration_discount_name"] = null;
                    $request_data["duration_discount_percent"] = null;
                    $request_data["duration_discount_amount"] = null;

                    if ($monthly_discount && $total_days >= 30) {
                        $discount_amount = get_percentage($monthly_discount, $total_amount);
                        $total_amount = round($total_amount - $discount_amount);
                        $request_data["duration_discount_name"] = "monthly";
                        $request_data["duration_discount_percent"] = $monthly_discount;
                        $request_data["duration_discount_amount"] = $discount_amount;
                    } elseif ($weekly_discount && $total_days >= 7) {
                        $discount_amount = get_percentage($weekly_discount, $total_amount);
                        $total_amount = round($total_amount - $discount_amount);
                        $request_data["duration_discount_name"] = "weekly";
                        $request_data["duration_discount_percent"] = $weekly_discount;
                        $request_data["duration_discount_amount"] = $discount_amount;
                    }
                    // ========== monthly and weekly discount end ========== //
                } else {
                    return ["status" => false, "message" => "Basis type not defined"];
                }
                // new listing discount
                $booking_total = Booking::where("user_id", auth()->id())->where("listing_id", $listing->id)->count();
                if ($booking_total < 3 && isset($listing->detail->listing_discount)) {
                    $new_booking_discount = get_percentage($listing->detail->listing_discount, $total_amount);
                    $total_amount = round($total_amount - $new_booking_discount);
                    $request_data["new_listing_discount"] = $listing->detail->listing_discount;
                    $request_data["new_listing_discount_amount"] = $new_booking_discount;
                }else{
                    $request_data["new_listing_discount"] = null;
                    $request_data["new_listing_discount_amount"] = null;
                }
                // new listing discount end
                // ========= season discount ========= //
                if(isset($listing->detail->season_start) && isset($listing->detail->season_end)){
                    $season_discount = seasonDiscount($request->check_in, $total_amount, $listing->detail);
                    if ($listing->detail->season_type == "increase") {
                        $total_amount = round($total_amount + $season_discount);
                        $season_type = $season_discount > 0 ? "increase" : null;

                    } else {
                        $total_amount = round($total_amount - $season_discount);
                        $season_type = $season_discount > 0 ? "decrease" : null;
                    }
                    $request_data["season_price"] = $season_discount;
                    $request_data["season_type"] = $season_type;
                }else{
                    $request_data["season_price"] = null;
                    $request_data["season_type"] = null;
                }
                // ========= season discount ========= //
                $request_data["total_amount"] = $total_amount;
                if ($listing->detail->basis_type == 'Hourly' && (is_null($request->check_in_time) || is_null($request->check_out_time))) {
                    return ["status" => false, "message" => "Check in and check out time is required"];
                }
                $cart = Cart::where("listing_id", $listing->id)->where("user_id", auth()->id())->first();
                if ($cart) {
                    $cart->update($request_data);
                    $cart_count = Cart::where("user_id", auth()->id())->count();
                    return ["status" => true, "message" => "Listing Cart updated", "data" => $cart_count];
                } else {
                    Cart::create($request_data);
                    $cart_count = Cart::where("user_id", auth()->id())->count();
                    return ["status" => true, "message" => "Listing Added to cart", "data" => $cart_count];
                }
            } else {
                return ["status" => false, "message" => "No listing found", "data" => null];
            }
        }catch(\Exception $e){
            return ["status" => false, "message" => $e->getMessage(), "data" => null];
        }

    }
    function delete_cart($cart_id)
    {
        $cart = Cart::find($cart_id);
        if ($cart) {
            $cart->delete();
            $cart_count = Cart::where("user_id", auth()->id())->count();
            return ["status" => true, "message" => "Cart deleted successfully", "data" => $cart_count];
        } else {
            return ["status" => false, "message" => "Cart not found"];
        }
    }
    function checkout()
    {
        try {
            if (auth()->user()->identity_verified  == "verified") {
                $carts = Cart::where("user_id", auth()->id())->get();
                $skyflow_bearer_token = skyflow_bearer_token();
                $cards = Http::withHeaders([
                    'Authorization' => $skyflow_bearer_token["tokenType"] . " " . $skyflow_bearer_token["accessToken"],
                    'Content-Type' => 'application/json',
                ])->post(skyflow_vault_url().'/v1/vaults/'.skyflow_vault_id().'/query', [
                    'query' => "SELECT * FROM credit_cards WHERE user_id = " . auth()->id()
                ]);
                $cards = $cards->json();
                if (!empty($carts)) {
                    return view('website.checkout', compact("carts", "cards"));
                } else {
                    return redirect("/")->with(["type" => "error", "message" => "Please reserve the booking", "title" => "No Booking found"]);
                }
            } else {
                return back()->with("verify", translate('user_account_setting.verify_identity_msg'));
            }
        } catch (\Exception $e) {
            return redirect()->route("cart")->with("message", $e->getMessage());
        }
    }

    function checkout_post(Request $request)
    {
        $request->validate([
            "cvc" => "required",
            "card_id" => "required"
        ]);
        try {
            Stripe::setApiKey(secret_key());
            $carts = Cart::whereUserId(auth()->id())->get();
            $get_card = Http::withHeaders([
                'Accept' => 'application/json',
                'X-SKYFLOW-ACCOUNT-ID' => skyflow_acc_id(),
                'Authorization' => skyflow_bearer_token()["tokenType"] . " " . skyflow_bearer_token()["accessToken"],
            ])->get(skyflow_vault_url() . "/v1/vaults/".skyflow_vault_id()."/credit_cards/{$request->card_id}?tokenization=true");
            foreach ($carts as $cart) {
                if ($cart) {
                    $listing = Listing::find($cart->listing_id);
                    if ($listing) {
                        $total = $cart->total_amount;
                        if ($get_card->successful() && isset($get_card["fields"])) {
                            $skyflow_payment_method = skyflow_payment_method($get_card["fields"], $request->cvc);
                            if (isset($skyflow_payment_method["id"])) {
                                $paymentIntent = \Stripe\PaymentIntent::create([
                                    'amount' => $total * 100,
                                    'currency' => 'usd',
                                    'payment_method' => $skyflow_payment_method["id"],
                                    'confirmation_method' => 'automatic',
                                    'confirm' => true,
                                    'return_url' => url("/"),
                                ]);
                                if (isset($paymentIntent["id"])) {
                                    $booking = new Booking();
                                    $booking->user_id = auth()->id();
                                    $booking->provider_id = $listing->user_id;
                                    $booking->listing_id = $listing->id;
                                    $booking->listing_basis = $cart->basis_type;
                                    $booking->total_days = $cart->total_days;
                                    $booking->total_hours = $cart->total_hours;
                                    $booking->check_in = $cart->check_in;
                                    if (!$cart->check_out || $cart->check_out == 0) {
                                        $booking->check_out = $cart->check_in;
                                    } else {
                                        $booking->check_out = $cart->check_out;
                                    }
                                    // $booking->check_out = $cart->check_out;
                                    $booking->check_in_time = $cart->check_in_time;
                                    $booking->check_out_time = $cart->check_out_time;
                                    $booking->guest = $cart->guest;
                                    if ($cart->basis_type == "Daily") {
                                        $per_day = $cart->listing->detail->per_day;
                                        $booking->per_day = $per_day;
                                        // $total = $per_day * $cart->total_days;
                                    } elseif ($cart->basis_type == "Hourly") {
                                        $per_hour = $cart->listing->detail->per_hour;
                                        // $total = $per_hour * $cart->total_days * $cart->total_hours;
                                        $booking->per_hour = $per_hour;
                                    } elseif ($cart->basis_type == "Tour") {
                                        $booking->no_adult = $cart->adult_number;
                                        $booking->per_adult = $cart->adult_number;
                                        $booking->no_child = $cart->child_number;
                                    }
        
                                    if ($listing->detail->monthly_discount && $cart->total_days >= 30) {
                                        $monthly_discount = ($listing->detail->monthly_discount / 100) * $cart->total_amount;
                                        $booking->duration_discount_name = "monthly";
                                        $booking->duration_discount_percent = $listing->detail->monthly_discount;
                                        $booking->duration_discount_amount = $monthly_discount;
                                        // $total = $total - $monthly_discount;
                                    } elseif ($listing->detail->weekly_discount && $cart->total_days >= 7) {
                                        $weekly_discount = ($listing->detail->weekly_discount / 100) * $cart->total_amount;
                                        $booking->duration_discount_name = "weekly";
                                        $booking->duration_discount_percent = $listing->detail->weekly_discount;
                                        $booking->duration_discount_amount = $weekly_discount;
                                        // $total = $total - $weekly_discount;
                                    }
                                    $booking->sub_total = $cart->sub_total;
                                    $booking->total_amount = $cart->total_amount;
                                    $booking->payment_intent_id = $paymentIntent["id"];
                                    // $booking->receipt_url = $charge->receipt_url;32
                                    if ($booking->save()) {
                                        // WalletService::deposit($booking->id, $listing->user_id, $total);
                                        $notification_users = User::whereIn("id", [2, $listing->user_id])->get();
                                        foreach ($notification_users as $notification_user) {
                                            $notification_user->notify(new BookingNotification($booking->id));
                                        }
                                        $cart->delete();
                                    }
                                }
                            }
                        }

                        // $card = Card::where("user_id", auth()->id())->where("id", $request->card_id)->firstOrFail();
                        // $token = Token::create([
                        //     "card" => [
                        //         "number"    => decrypt($card->card_number),
                        //         "exp_month" => decrypt($card->month),
                        //         "exp_year"  => decrypt($card->year),
                        //         "cvc"       => $request->cvc,
                        //         "name"      => isset($card->card_holder_name) ? decrypt($card->card_holder_name) : "",
                        //     ]
                        // ]);
                        // $total = $cart->total_amount;
                        // $charge = Charge::create([
                        //     "amount" => $total * 100,
                        //     "currency" => "usd",
                        //     "source" => $token->id,
                        //     "description" => auth()->user()->name . " booked the $listing->name on $cart->check_in"
                        // ]);
                    }
                }
            }
            return redirect("bookings")->with(["message" => "Booking Placed Successfully", "type" => "success", "title" => "Success"]);
        } catch (\Exception $e) {
            return back()->with(["message" => $e->getMessage(), "type" => "error", "title" => "Something went wrong"]);
        }
    }
}
